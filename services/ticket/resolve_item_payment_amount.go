package ticket

import (
	"context"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"time"
)

// item支付统计表
func resolveItemPaymentAmount(ctx context.Context, ticket *model.Ticket) (resolveResult []model.Resolve, err error) {
	// 拆解到item级别
	allSkus := stepItemSplit(stepItemPre(ticket))

	// 处理item的折扣、支付分摊
	// todo: itemDiscountMap也拆分好了，用于item折扣入库
	itemList, _, itemPaymentMap, paySkuMap, paySalesSumAmount, payNonSalesSumAmount := stepItemApportion(ticket, allSkus)
	logger.Pre().Infof("paySalesSumAmount:%f,payNonSalesSumAmount:%f", paySalesSumAmount.InexactFloat64(), payNonSalesSumAmount.InexactFloat64())

	// 入库前数据准备
	now := time.Now()
	bd, _ := time.Parse(config.DateFormat, ticket.BusDate)
	st, _ := time.Parse(config.DateTimeFormat, ticket.StartTime)
	et := ticket.GetOrderTime()

	// 整单支付项总和
	orderPayAmount := decimal.NewFromFloat(ticket.Amounts.PayAmount)
	orderRounding := decimal.NewFromFloat(ticket.Amounts.Rounding)
	orderSurChargeAmount := decimal.NewFromFloat(ticket.Amounts.SurChargeAmount)
	orderPaidTotal := orderPayAmount.Sub(orderRounding).Add(orderSurChargeAmount)
	logger.Pre().Infof("orderPaidTotal:%v", orderPaidTotal.InexactFloat64())

	// main_seq,seq
	var mainSeq, seq uint64 = 0, 0
	// double check salesTotal,nonSalesTotal match
	nonSalesTotal, salesTotal := decimal.NewFromFloat(0), decimal.NewFromFloat(0)

	// prepare before insert
	for k, item := range itemList {
		// item序号(从0开始展示)
		if k > 0 {
			seq++
		}
		if item.ParentId == "" {
			// main item序号
			mainSeq++
		}
		// 该item被几次支付项分摊： [product_id_shopping_id -> ["seq1"->pay1,"seq2"->pay2,...]]
		payments, okItemPayment := itemPaymentMap[item.Id+"_"+item.ShoppingId]

		if okItemPayment {
			// paySkuMap,记录stepSkuList中的商品是否命中pay中的applyShoppingId,key=product.ShoppingId,val=product.ID
			_, okPaySku := paySkuMap[item.ShoppingId]
			for _, pay := range payments {
				// 默认：设置商品--支付项--均摊价
				transAmount := decimal.NewFromFloat(0)

				// item没价格，则分摊金额为0
				if item.Price != 0 {
					// 商品行存在单品 && 购买id = 支付项购买id
					if okPaySku && pay.ApplyShoppingId == item.ShoppingId {
						transAmount = pay.TransAmount
					} else if !okPaySku {
						transAmount = pay.TransAmount
					}
				}

				// item
				rsm := &model.SalesItemPaymentsAmounts{
					ID:                utils.GetHexUUid(ctx),
					PartnerId:         cast.ToInt64(ticket.Store.PartnerId),
					StoreID:           cast.ToInt64(ticket.Store.Id),
					StoreCode:         ticket.Store.Code,
					StoreName:         ticket.Store.Name,
					TicketID:          ticket.TicketId,
					EticketId:         ticket.Id,
					TicketUno:         ticket.TicketUno,
					TicketStatus:      ticket.Status.String(),
					TakeMealNo:        ticket.TakemealNumber,
					ItemID:            cast.ToInt64(item.Id),
					ParentItemID:      cast.ToInt64(item.ParentId),
					ItemCode:          item.Code,
					ItemName:          item.Name,
					ItemType:          item.Type, //item类型：NORMAL(普通商品),SET(套餐商品),FLEX_SET(灵活套餐商品),ADDITION(加料),REMARK(属性)
					MainSeq:           mainSeq,
					Seq:               seq,
					Price:             item.Price,
					Qty:               cast.ToInt64(item.Qty),
					Amount:            item.Amount,
					OrderPaidTotal:    orderPaidTotal.InexactFloat64(),       //所有商品支付净额总和
					SalesPayAmount:    paySalesSumAmount.InexactFloat64(),    //所有支付项销售总和
					NonSalesPayAmount: payNonSalesSumAmount.InexactFloat64(), //所有支付项非销售总和
					TransID:           cast.ToInt64(pay.Id),
					TransCode:         pay.TransCode,
					TransName:         pay.TransName,
					TransAttr:         pay.TransAttr,
					TransAmount:       transAmount.InexactFloat64(),
					BusDate:           time.Date(bd.Year(), bd.Month(), bd.Day(), 0, 0, 0, 0, time.UTC),
					StartTime:         st,
					EndTime:           et,
					Created:           now,
				}
				resolveResult = append(resolveResult, rsm)
				if pay.TransAttr == "SALES" {
					salesTotal = salesTotal.Add(transAmount)
				} else {
					nonSalesTotal = nonSalesTotal.Add(transAmount)
				}
			}
		}
	}
	logger.Pre().Infof("[resolveItemPaymentAmount] insert:%s", "finish")
	logger.Pre().Infof("salesTotal added up:%v", salesTotal.String())
	logger.Pre().Infof("nonSalesTotal added up:%v", nonSalesTotal.String())
	return
}

// 预处理
func stepItemPre(ticket *model.Ticket) []*model.TicketProduct {
	var noZeroProducts []*model.TicketProduct
	for _, product := range ticket.Products {
		// 商品单价不为0 or 商品实收不为0
		if product.Price != 0 || product.SumNetAmount != 0 {
			noZeroProducts = append(noZeroProducts, product)
		} else {
			if ticket.Status != "SALE" {
				// 部分退负单，如果只退了 饿了么的 加料，需要计算
				// 饿了么加料在合阔这边算作加价属性
				noZeroProducts = append(noZeroProducts, product)
			}
		}
	}
	return noZeroProducts
}

// 分解item，摊平
func stepItemSplit(products []*model.TicketProduct) (skuList []*model.TicketProduct) {
	// 用于跟踪已添加的商品，避免重复添加 (使用ProductId+ShoppingId作为唯一标识)
	addedItems := make(map[string]bool)

	for _, product := range products {
		// 按结构判断
		if len(product.ComboItems) > 0 {
			product.Type = model.ProductTypeSET.String()
		} else {
			product.Type = model.ProductTypeNORMAL.String()
		}

		// 根据类型拆分
		switch product.Type {
		case model.ProductTypeNORMAL.String():
			// 1.普通商品
			//    ｜___属性
			//    ｜___加料
			//       ｜___加料商品的属性
			// 1.0 自身
			// 250403 价格 = amount/qty
			product.Price = product.Amount / float64(product.Qty)

			// 检查是否已添加过该商品 (使用ProductId+ShoppingId作为唯一标识)
			productKey := product.Id + "_" + product.ShoppingId
			if !addedItems[productKey] {
				skuList = append(skuList, product)
				addedItems[productKey] = true
			}
			// 1.1 属性
			for _, skuRemark := range product.SkuRemark {
				// 属性也算作一个sku
				ticketProduct := &model.TicketProduct{}
				// 属性不需要加shoppingID
				ticketProduct.Id = skuRemark.Name.Id
				ticketProduct.ParentId = product.Id
				ticketProduct.Qty = product.Qty
				// 250403 无论普通商品、套餐商品，属性价格置0
				//ticketProduct.Price = skuRemark.Values.Price
				ticketProduct.Price = 0
				ticketProduct.Code = skuRemark.Values.Code
				ticketProduct.Name = skuRemark.Values.Name
				ticketProduct.Type = model.ProductTypeREMARK.String()
				skuList = append(skuList, ticketProduct)
			}
			// 1.2 加料
			for _, accessory := range product.Accessories {
				// 商品在订单中唯一id - 使用加料自己的ShoppingId，避免重复分摊
				//accessory.ShoppingId = product.ShoppingId
				accessory.ParentId = product.Id
				accessory.Type = model.ProductTypeADDITION.String()

				// 检查是否已添加过该加料商品 (使用ProductId+ShoppingId作为唯一标识)
				accessoryKey := accessory.Id + "_" + accessory.ShoppingId
				if !addedItems[accessoryKey] {
					skuList = append(skuList, accessory)
					addedItems[accessoryKey] = true
				}
			}
		case model.ProductTypeSET.String(), model.ProductTypeFLEXSET.String():
			// 2.套餐商品 [无属性]
			//    ｜___套餐头商品
			//    ｜___子项商品
			//       ｜___子项商品的属性
			//       ｜___子项商品的加料
			//          ｜___子项商品的加料的属性
			// 2.0 套餐头
			// 250403 套餐头价格 = amount/qty
			product.Price = product.Amount / float64(product.Qty)

			// 检查是否已添加过该套餐商品 (使用ProductId+ShoppingId作为唯一标识)
			productKey := product.Id + "_" + product.ShoppingId
			if !addedItems[productKey] {
				skuList = append(skuList, product)
				addedItems[productKey] = true
			}
			for _, comboItem := range product.ComboItems {
				// 2.1 子项商品
				comboItem.ParentId = product.Id
				// 250403 子项商品自身不算价格，因为已在套餐头上参与算价
				comboItem.Price = 0

				// 检查是否已添加过该子项商品 (使用ProductId+ShoppingId作为唯一标识)
				comboItemKey := comboItem.Id + "_" + comboItem.ShoppingId
				if !addedItems[comboItemKey] {
					skuList = append(skuList, comboItem)
					addedItems[comboItemKey] = true
				}
				// 2.2 子项商品属性
				for _, skuRemark := range comboItem.SkuRemark {
					ticketProduct := &model.TicketProduct{}
					// 属性不需要加shoppingID
					ticketProduct.Id = skuRemark.Name.Id
					ticketProduct.ParentId = product.Id
					ticketProduct.Qty = product.Qty
					// 250403 无论普通商品、套餐商品，属性价格置0
					//ticketProduct.Price = skuRemark.Values.Price
					ticketProduct.Price = 0
					ticketProduct.Code = skuRemark.Values.Code
					ticketProduct.Name = skuRemark.Values.Name
					ticketProduct.Type = model.ProductTypeREMARK.String()
					skuList = append(skuList, ticketProduct)
				}
				// 2.3 子项商品加料
				for _, accessory := range comboItem.Accessories {
					// 2.3 套餐商品，子项商品加料（不会再嵌套了，已经最后一层）
					// 商品在订单中唯一id - 使用加料自己的ShoppingId，避免重复分摊
					//accessory.ShoppingId = product.ShoppingId
					accessory.ParentId = product.Id
					accessory.Type = model.ProductTypeADDITION.String()
					skuList = append(skuList, accessory)
				}
			}
		default:
			continue
		}
	}
	return skuList
}

// 分摊普通item的折扣、支付
func stepItemApportion(ticket *model.Ticket, stepSkuList []*model.TicketProduct) ([]*model.TicketProduct, map[string]map[string]model.ItemPromotion, map[string]map[string]model.ItemPayment, map[string]string, decimal.Decimal, decimal.Decimal) {
	// 1.折扣分摊：均摊金额 = 折扣金额 * (此item金额 / 所有参与的item金额) item：qty * 单品折扣+ qty * 订单折扣
	// 2.支付分摊：支付金额 = 支付金额 * (此item金额 / 所有参与的item金额) item：支付方式1 + 支付方式2 + ...
	// 2.1 支付识别：若支付里有applyShoppingId，则只分配那个商品，否则默认参与均摊

	// discount
	// e.g. 券1: cost + tp + m(2) + p
	// e.g. 券2: cost + tp + m(0) + p

	// payAmount
	// e.g. 支付1: cost（16.5） + tp + m + p
	// e.g. 支付2: cost(1.62) + tp + m + p

	// a.折扣
	// 折扣电子小票已分摊，有几种促销，就直接展开：
	// 商品a -> [折扣1 -> 1.23,折扣2 -> 2.34]
	productsDiscountMap := map[string]map[string]model.ItemPromotion{}
	for _, pro := range ticket.Promotions {
		// 每次遍历促销时，先确定下交易类型: 默认SALES，否则如果含有非销售类型，则为非销售
		transAttr := "SALES"
		if lo.Contains(pro.PromotionInfo.TransactionTypes, "NON_SALES") {
			transAttr = "NON_SALES"
		}

		// 订单级别/商品级别折扣，指定/所有商品都参与
		// 1.普通商品
		//    ｜___属性
		//    ｜___加料
		//       ｜___加料商品的属性
		// 2.套餐商品 [无属性]
		//    ｜___套餐头商品
		//    ｜___子项商品
		//       ｜___子项商品的属性
		//       ｜___子项商品的加料
		//          ｜___子项商品的加料的属性(折扣最多到子项商品加料，加料属性item沿用其父级折扣）
		for _, product := range pro.Products {
			key := product.KeyId
			//key := product.KeyId + "_" + product.ShoppingId
			if _, ok := productsDiscountMap[key]; !ok {
				productsDiscountMap[key] = make(map[string]model.ItemPromotion)
			}
			itemPromotion := productsDiscountMap[key][pro.PromotionInfo.PromotionId]
			itemPromotion.Id = pro.PromotionInfo.PromotionId
			itemPromotion.TransCode = pro.PromotionInfo.PromotionCode
			itemPromotion.TransName = pro.PromotionInfo.Name
			itemPromotion.TransAttr = transAttr
			itemPromotion.TransAmount = decimal.NewFromFloat(product.Discount)
			productsDiscountMap[key][pro.PromotionInfo.PromotionId] = itemPromotion

			// 折扣里，商品里加料sku
			for _, accessory := range product.Accessories {
				accessoryKey := accessory.KeyId
				//accessoryKey := accessory.KeyId + "_" + product.ShoppingId
				if _, ok := productsDiscountMap[accessoryKey]; !ok {
					productsDiscountMap[accessoryKey] = make(map[string]model.ItemPromotion)
				}
				accessoryPromotion := productsDiscountMap[accessoryKey][pro.PromotionInfo.PromotionId]
				accessoryPromotion.Id = pro.PromotionInfo.PromotionId
				accessoryPromotion.TransCode = pro.PromotionInfo.PromotionCode
				accessoryPromotion.TransName = pro.PromotionInfo.Name
				accessoryPromotion.TransAttr = transAttr
				accessoryPromotion.TransAmount = decimal.NewFromFloat(accessory.Discount)
				productsDiscountMap[accessoryKey][pro.PromotionInfo.PromotionId] = accessoryPromotion
			}
		}

		// 每种promo看情况进行摊分
		// 1.整单券 2.商品券
		// 商品独享，全额使用
		if pro.PromotionInfo.DiscountLevel == model.PromotionDiscountLevelSku.String() {
			for _, product := range stepSkuList {
				key := product.Id
				//key := product.Id + "_" + product.ShoppingId
				if _, ok := productsDiscountMap[key]; !ok {
					productsDiscountMap[key] = make(map[string]model.ItemPromotion)
				}
				itemPromotion := productsDiscountMap[key][pro.PromotionInfo.PromotionId]
				itemPromotion.Id = pro.PromotionInfo.PromotionId
				itemPromotion.TransCode = pro.PromotionInfo.PromotionCode
				itemPromotion.TransName = pro.PromotionInfo.Name
				itemPromotion.TransAttr = transAttr
				// 商品级别item才重新计算(折扣>0才有折扣）(todo: 商品价格1分钱，券-2元，-1.99这种怎么弄)
				if itemPromotion.TransAmount.GreaterThan(decimal.NewFromFloat(0)) {
					itemPromotion.TransAmount = decimal.NewFromFloat(pro.Source.Discount)
					productsDiscountMap[key][pro.PromotionInfo.PromotionId] = itemPromotion
				}
			}
		}

		// 整单摊分
		if pro.PromotionInfo.DiscountLevel == model.PromotionDiscountLevelOrder.String() {
			for _, product := range stepSkuList {
				key := product.Id
				//key := product.Id + "_" + product.ShoppingId
				if _, ok := productsDiscountMap[key]; !ok {
					productsDiscountMap[key] = make(map[string]model.ItemPromotion)
				}
				itemPromotion := productsDiscountMap[key][pro.PromotionInfo.PromotionId]
				itemPromotion.Id = pro.PromotionInfo.PromotionId
				itemPromotion.TransCode = pro.PromotionInfo.PromotionCode
				itemPromotion.TransName = pro.PromotionInfo.Name
				itemPromotion.TransAttr = transAttr
				// 单价 * 数量 / promo总额(折扣>0才有折扣）
				qty := decimal.NewFromFloat(float64(product.Qty))
				price := decimal.NewFromFloat(product.Price)
				promoAmount := decimal.NewFromFloat(pro.Source.Discount)
				grossAmount := decimal.NewFromFloat(ticket.Amounts.GrossAmount)
				if itemPromotion.TransAmount.GreaterThan(decimal.NewFromFloat(0)) {
					itemPromotion.TransAmount = promoAmount.Mul(qty).Mul(price).Div(grossAmount)
					productsDiscountMap[key][pro.PromotionInfo.PromotionId] = itemPromotion
				}
			}
		}
	}

	// b.支付
	// 有几种支付，就直接展开：
	// 商品a_shoppingId -> [支付1 -> 1.23,支付2 -> 2.34]
	productsPaymentMap := map[string]map[string]model.ItemPayment{}

	// 先计算单品支付项合计金额
	applySumAmount := decimal.NewFromFloat(0)
	// 创建payApplyMap,记录pay中是否有applyShoppingId,key=Pay.SeqId，val=bool
	payApplyMap := map[string]bool{}
	// 创建paySkuMap,记录stepSkuList中的商品是否命中pay中的applyShoppingId,key=product.ShoppingId,val=product.ID
	paySkuMap := map[string]string{}

	// 创建stepSkuMap,key=product.Id,val=product
	stepSkuMap := map[string]*model.TicketProduct{}

	// 支付项合计销售金额
	paySalesSumAmount := decimal.NewFromFloat(0)
	// 支付项合计非销售金额
	payNonSalesSumAmount := decimal.NewFromFloat(0)
	for _, pay := range ticket.Payments {
		// 先分别统计： 支付项合计销售金额、支付项合计非销售金额
		receivable := decimal.NewFromFloat(pay.Receivable)
		rounding := decimal.NewFromFloat(pay.Rounding)
		surcharge := decimal.NewFromFloat(pay.SurChargeAmount)
		overflow := decimal.NewFromFloat(pay.Overflow)
		userPaidTotal := receivable.Sub(rounding).Add(surcharge).Add(overflow)
		if lo.Contains(pay.TransactionTypes, "NON_SALES") {
			payNonSalesSumAmount = payNonSalesSumAmount.Add(userPaidTotal)
		} else {
			paySalesSumAmount = paySalesSumAmount.Add(userPaidTotal)
		}

		// 商品识别
		if pay.ApplyShoppingId != "" {
			payApplyMap[pay.SeqId] = true
		}
		for _, product := range stepSkuList {
			// 填充stepSkuMap
			stepSkuMap[product.Id] = product

			// 命中，则加入map
			if pay.ApplyShoppingId != "" && pay.ApplyShoppingId == product.ShoppingId && product.ParentId == "" {
				paySkuMap[pay.ApplyShoppingId] = product.Id
				// 并累加单品支付金额到applySumAmount
				receivable := decimal.NewFromFloat(pay.Receivable)
				rounding := decimal.NewFromFloat(pay.Rounding)
				surcharge := decimal.NewFromFloat(pay.SurChargeAmount)
				overflow := decimal.NewFromFloat(pay.Overflow)
				userPaidTotal := receivable.Sub(rounding).Add(surcharge).Add(overflow)
				logger.Pre().Infof("单品支付项，分项金额->%s:%f｜%s:%f｜ %s:%f｜ %s:%f｜%s:%f", "receivable", pay.Receivable, "rounding", pay.Rounding, "surcharge", pay.SurChargeAmount, "overflow", overflow.InexactFloat64(), "userPaidTotal", userPaidTotal.InexactFloat64())
				applySumAmount = applySumAmount.Add(userPaidTotal)
			}
		}
	}
	logger.Pre().Infof("单品支付项，合计金额->%s:%f", "applySumAmount", applySumAmount.InexactFloat64())
	logger.Pre().Infof("支付项合计销售金额->%s:%f", "paySalesSumAmount", paySalesSumAmount.InexactFloat64())
	logger.Pre().Infof("支付项合计非销售金额->%s:%f", "payNonSalesSumAmount", payNonSalesSumAmount.InexactFloat64())

	// 将ticket.Payments里面元素按是否有applyShoppingId，分成两组
	with, without := groupPaymentsByApplyShoppingId(ticket.Payments)
	logger.Pre().Infof("按是否有applyShoppingId分组，with:%v,withou:%v", len(with), len(without))

	// 接着开始支付分摊
	// 有applyShoppingId的，先处理
	for _, pay := range with {
		// 每次遍历支付时，确定类型是"销售"/"非销售"
		transAttr := "SALES"
		if lo.Contains(pay.TransactionTypes, "NON_SALES") {
			transAttr = "NON_SALES"
		}

		// 1. 有支付命中单品支付项
		if _, ok := payApplyMap[pay.SeqId]; ok {
			// 且命中商品中的ShoppingId，则采取单品分摊算法计算
			if _, ok := paySkuMap[pay.ApplyShoppingId]; ok {
				// paySkuMap[pay.ApplyShoppingId] 即为 product.Id
				applyProductId := paySkuMap[pay.ApplyShoppingId]

				receivable := decimal.NewFromFloat(pay.Receivable)
				rounding := decimal.NewFromFloat(pay.Rounding)
				surcharge := decimal.NewFromFloat(pay.SurChargeAmount)
				overflow := decimal.NewFromFloat(pay.Overflow)
				userPaidTotal := receivable.Sub(rounding).Add(surcharge).Add(overflow)
				logger.Pre().Infof("单品支付项，各项金额->%s:%f｜%s:%f｜ %s:%f｜ %s:%f｜%s:%f", "receivable", pay.Receivable, "rounding", pay.Rounding, "surcharge", pay.SurChargeAmount, "overflow", overflow.InexactFloat64(), "userPaidTotal", userPaidTotal.InexactFloat64())

				// 检查并初始化嵌套映射
				if _, exists := productsPaymentMap[applyProductId+"_"+pay.ApplyShoppingId]; !exists {
					productsPaymentMap[applyProductId+"_"+pay.ApplyShoppingId] = make(map[string]model.ItemPayment)
				}
				itemPayment, ok := productsPaymentMap[applyProductId+"_"+pay.ApplyShoppingId][pay.SeqId]
				if !ok {
					itemPayment = model.ItemPayment{}
				}
				itemPayment.Id = pay.Id
				itemPayment.TransCode = pay.TransCode
				itemPayment.TransName = pay.TransName
				// sales/non-sales
				itemPayment.TransAttr = transAttr
				itemPayment.ApplyShoppingId = pay.ApplyShoppingId

				// 单品支付项，只能用于单个商品，但支付次数可以多次
				price := decimal.NewFromFloat(0)
				if _, exists := stepSkuMap[applyProductId]; exists {
					price = decimal.NewFromFloat(stepSkuMap[applyProductId].Price)
				}

				// 单品支付摊分: 取支付金额和商品价格的较小值
				itemPayment.TransAmount = decimal.Min(price, userPaidTotal)
				productsPaymentMap[applyProductId+"_"+pay.ApplyShoppingId][pay.SeqId] = itemPayment
				logger.Pre().Infof("单品支付|商品:%s,购物id:%s,支付金额:%f,分摊金额:%f",
					stepSkuMap[applyProductId].Name, stepSkuMap[applyProductId].ShoppingId,
					userPaidTotal.InexactFloat64(), itemPayment.TransAmount.InexactFloat64())
				// 算完后跳过该支付项
				continue
			}
		}
	}

	// 无applyShoppingId的，后处理
	for _, pay := range without {
		// 每次遍历支付时，确定类型是"销售"/"非销售"
		transAttr := "SALES"
		if lo.Contains(pay.TransactionTypes, "NON_SALES") {
			transAttr = "NON_SALES"
		}

		// 2.订单级别/商品级别支付，指定/所有商品都参与
		// payAmount := decimal.NewFromFloat(pay.PayAmount)
		// e.g. 用户实付 = 应付（100.8） - 抹0（-0.2,谭仔hk接上游pos处理成了负数,没按溢收来处理）+ 三方支付渠道手续费（0）= 100.8 - (-0.2) + 0 = 101
		receivable := decimal.NewFromFloat(pay.Receivable)
		rounding := decimal.NewFromFloat(pay.Rounding)
		surcharge := decimal.NewFromFloat(pay.SurChargeAmount)
		overflow := decimal.NewFromFloat(pay.Overflow)
		userPaidTotal := receivable.Sub(rounding).Add(surcharge).Add(overflow)
		//logger.Pre().Infof("普通支付分摊前各项金额->%s:%f｜%s:%f｜ %s:%f｜ %s:%f｜%s:%f", "receivable", pay.Receivable, "rounding", pay.Rounding, "surcharge", pay.SurChargeAmount, "overflow",overflow.InexactFloat64(),"userPaidTotal", userPaidTotal.InexactFloat64())
		for _, product := range stepSkuList {
			// 跳过之前单项分摊过的商品
			if _, ok := paySkuMap[product.ShoppingId]; ok && product.ParentId == "" {
				// e.g. 下了若干个单品花费金额x，用了若干次券抵扣金额y，剩余x-y的金额需要其他支付项分摊
				if ppm, exists := productsPaymentMap[product.Id+"_"+product.ShoppingId]; exists {
					if len(ppm) >= product.Qty {
						continue
					}
				}
			}

			// split equally
			qty := decimal.NewFromFloat(float64(product.Qty))
			if ppm, exists := productsPaymentMap[product.Id+"_"+product.ShoppingId]; exists {
				// 单品分摊过的商品，需要减去已分摊的数量
				if _, ok := paySkuMap[product.ShoppingId]; ok && product.ParentId == "" {
					if len(ppm) < product.Qty {
						qty = decimal.NewFromInt(int64(product.Qty)).Sub(decimal.NewFromInt(int64(len(ppm))))
					}
				}
			}
			price := decimal.NewFromFloat(product.Price)
			grossAmount := decimal.NewFromFloat(ticket.Amounts.GrossAmount)
			// 订单商品总计实付，需要减去单品支付项目合计
			grossAmount = grossAmount.Sub(applySumAmount)
			//logger.Pre().Infof("订单实付(减去单品支付项目合计)->%s:%f", "grossAmount", grossAmount.InexactFloat64())

			if grossAmount.Equals(decimal.NewFromFloat(0)) {
				// 如果商品上的金额为0，说明整单都是单品分摊，不需要再分摊，跳出本层循环
				break
			}

			// 检查并初始化嵌套映射
			if _, exists := productsPaymentMap[product.Id+"_"+product.ShoppingId]; !exists {
				productsPaymentMap[product.Id+"_"+product.ShoppingId] = make(map[string]model.ItemPayment)
			}

			itemPayment, ok := productsPaymentMap[product.Id+"_"+product.ShoppingId][pay.SeqId]
			if !ok {
				itemPayment = model.ItemPayment{}
			}
			itemPayment.Id = pay.Id
			itemPayment.TransCode = pay.TransCode
			itemPayment.TransName = pay.TransName
			// sales/non-sales
			itemPayment.TransAttr = transAttr
			// 普通支付摊分: 支付项金额 * (此item金额 / 订单总金额)
			itemPayment.TransAmount = userPaidTotal.Mul(qty).Mul(price).Div(grossAmount)
			logger.Pre().Infof("普通支付|商品:%s,支付金额:%f,数量:%d,单价:%f,总额:%f,分摊:%f",
				product.Name, userPaidTotal.InexactFloat64(), product.Qty, product.Price,
				grossAmount.InexactFloat64(), itemPayment.TransAmount.InexactFloat64())
			productsPaymentMap[product.Id+"_"+product.ShoppingId][pay.SeqId] = itemPayment
		}
	}

	// 处理尾差，确保分摊金额总和等于原始支付金额
	for _, pay := range ticket.Payments {
		// 计算支付项实际支付金额
		userPaidTotal := decimal.NewFromFloat(pay.Receivable).Sub(decimal.NewFromFloat(pay.Rounding)).Add(decimal.NewFromFloat(pay.SurChargeAmount)).Add(decimal.NewFromFloat(pay.Overflow))

		// 计算总和和找出最大分摊金额
		totalAllocated := decimal.NewFromFloat(0)
		maxAmount := decimal.NewFromFloat(0)
		maxAmountKey := ""

		for productKey, paymentMap := range productsPaymentMap {
			if itemPayment, ok := paymentMap[pay.SeqId]; ok {
				roundedAmount := itemPayment.TransAmount.Round(2)
				totalAllocated = totalAllocated.Add(roundedAmount)

				if roundedAmount.GreaterThan(maxAmount) {
					maxAmount = roundedAmount
					maxAmountKey = productKey
				}
			}
		}

		// 计算尾差并调整
		diff := userPaidTotal.Sub(totalAllocated)
		logger.Pre().Infof("支付项:%s,原始金额:%f,分摊后总和:%f,尾差:%f",
			pay.TransName, userPaidTotal.InexactFloat64(), totalAllocated.InexactFloat64(), diff.InexactFloat64())

		// 将尾差加到最大分摊金额的商品上
		if !diff.Equal(decimal.NewFromFloat(0)) && maxAmountKey != "" {
			itemPayment := productsPaymentMap[maxAmountKey][pay.SeqId]
			itemPayment.TransAmount = itemPayment.TransAmount.Round(2).Add(diff)
			productsPaymentMap[maxAmountKey][pay.SeqId] = itemPayment

			logger.Pre().Infof("尾差调整|商品:%s,原分摊金额:%f,调整后金额:%f",
				maxAmountKey, maxAmount.InexactFloat64(), itemPayment.TransAmount.InexactFloat64())
		}
	}

	// 对所有商品的分摊金额进行四舍五入
	for productKey, paymentMap := range productsPaymentMap {
		for payId, itemPayment := range paymentMap {
			itemPayment.TransAmount = itemPayment.TransAmount.Round(2)
			paymentMap[payId] = itemPayment
		}
		productsPaymentMap[productKey] = paymentMap
	}

	// 分摊完毕，返回
	return stepSkuList, productsDiscountMap, productsPaymentMap, paySkuMap, paySalesSumAmount, payNonSalesSumAmount
}

// groupPaymentsByApplyShoppingId 函数将 TicketPayment 按 ApplyShoppingId 是否存在分组
func groupPaymentsByApplyShoppingId(payments []model.TicketPayment) (with []model.TicketPayment, without []model.TicketPayment) {
	for _, payment := range payments {
		if payment.ApplyShoppingId != "" {
			with = append(with, payment)
		} else {
			without = append(without, payment)
		}
	}
	return
}
